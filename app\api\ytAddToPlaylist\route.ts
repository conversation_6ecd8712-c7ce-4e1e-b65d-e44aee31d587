import { NextRequest, NextResponse } from "next/server";
import { YT_AddToPlaylist } from "./business";
import { SongModel } from "@/app/models/Song";


// Update
export async function POST(request: NextRequest) {
    try {
        // Get request data including auth credentials
        const requestData:{ playlistUrl:string, song:SongModel, ytMusicAuth:string, ytMusicCookie:string } = await request.json();

        // Validate required parameters
        if (!requestData.playlistUrl || !requestData.song) {
            return NextResponse.json({ error: "Missing required parameters" }, { status: 400 });
        }

        if (!requestData.ytMusicAuth || !requestData.ytMusicCookie) {
            return NextResponse.json({ error: "Missing YouTube Music authentication credentials" }, { status: 400 });
        }

        // Create headers object with auth credentials
        const authHeaders = {
            "authorization": requestData.ytMusicAuth,
            "cookie": requestData.ytMusicCookie
        };

        // Call business logic with auth credentials
        await YT_AddToPlaylist(
            requestData.playlistUrl,
            [requestData.song],
            requestData.ytMusicAuth,
            requestData.ytMusicCookie
        );

        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}