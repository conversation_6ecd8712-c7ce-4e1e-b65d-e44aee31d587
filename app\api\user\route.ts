import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import bcrypt from "bcryptjs";
import <PERSON><PERSON> from "stripe";
import { CreateUser, GetUserByEmail, UpdateUser } from "./business";
import { UserModel } from "@/app/models/User";

// Get by email
export async function GET(request: NextRequest) {

    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const userEmail = params.get("userEmail")!;
        const result = await GetUserByEmail(userEmail);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// Create
export async function PUT(request: NextRequest) {

    try {

        const requestData:{ userData:UserModel, password:string } = await request.json();
        const userData = requestData.userData;
        const password = requestData.password;

        // Check if email already exists
        if ((await GetUserByEmail(userData.Email)) != null) {
            return NextResponse.json({ error: "This email already exists!" }, { status: 500 });
        }

        userData.PasswordHash = await bcrypt.hash(password, 12);

        await CreateUser(userData);
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }

}

// Update
export async function POST(request: NextRequest) {
    try {

        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

        const userData:UserModel = await request.json();

        await UpdateUser(userData);

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}