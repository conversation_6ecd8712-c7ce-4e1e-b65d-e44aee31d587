import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { AdjustItems } from "../business";

// Adjust Items - POST method
export async function POST(request: NextRequest) {
    try {
        unstable_noStore();
        
        const requestData = await request.json();
        const { id, completedItems } = requestData;
        
        // Validate required parameters
        if (!id) {
            return NextResponse.json({ error: "Missing required parameter: id" }, { status: 400 });
        }
        
        if (!completedItems || !Array.isArray(completedItems)) {
            return NextResponse.json({ error: "Missing or invalid parameter: completedItems must be an array" }, { status: 400 });
        }
        
        // Call business function
        await AdjustItems(id, completedItems);
        
        return NextResponse.json({ status: 200 });
        
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error: error instanceof Error ? error.message : error }, { status: 500 });
    }
}
