"use client"

import styles from "./page.module.scss";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useSession } from "next-auth/react";
import JC_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_Field from "../components/JC_Field/JC_Field";
import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Utils } from "../Utils";
import { D_UserConfig, UserConfigModel } from "../models/UserConfig";
import { PlaylistModel } from "../models/Playlist";
import { UserProcessProgressModel, D_UserProcessProgress, ProgressData_NewSongs } from "../models/UserProcessProgress";
import { ArtistFullModel, ArtistModel } from "../models/Artist";
import { D_UserArtistSubscription, UserArtistSubscriptionModel } from "../models/UserArtistSubscription";
import { SongModel } from "../models/Song";
import { FieldTypeEnum } from "../enums/FieldType";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { UserProcessProgressCodeEnum } from "../enums/UserProcessProgressCode";
import { YtAddItem } from "../apiServices/ytAddItem";
import JC_Checkbox from "../components/JC_Checkbox/JC_Checkbox";
import JC_Button from "../components/JC_Button/JC_Button";

export default function NewSongs() {

    const session = useSession();
    const userId = session.data?.user.Id ?? JC_Utils.getLocalUserId();

    // - STATE - //

    // Initialised
    const [isInitialised, setIsInitialised] = useState<boolean>(false);
    // Loading
    const [processHasRan, setProcessHasRan] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isPlaylistUrlLoading, setIsPlaylistUrlLoading] = useState<boolean>(false);
    // Creating Subs Mode
    const [inCreatingSubsMode, setInCreatingSubsMode] = useState<boolean>(false);
    // Subscriptions
    const [subscriptions, setSubscriptions] = useState<UserArtistSubscriptionModel[]>([]);
    // User Config
    const [userConfig, setUserConfig] = useState<UserConfigModel>();
    const [yourPlaylistData, setYourPlaylistData] = useState<PlaylistModel|null>();
    const [yourPlaylistName, setYourPlaylistName] = useState<string|null>();
    const [yourPlaylistError, setYourPlaylistError] = useState<string>("");
    // Progress
    const [progressItems, setProgressItems] = useState<UserArtistSubscriptionModel[]>([]);
    const [showNew, setShowNew] = useState<boolean>(false);
    // Authentication
    const [isAuthorized, setIsAuthorized] = useState<boolean>(true);

    useEffect(
        () => { setTimeout(() => document.getElementById("processContainer") != null ? document.getElementById("processContainer")!.scrollTop = (document.getElementById("processContainer")?.scrollHeight??1000)+200 : null, 50); },
        [isLoading, progressItems, showNew]
    );

    // - INITIALISE - //

    useEffect(() => {
        // Initialize the page
        const initializePage = async () => {
            try {
                // Get YouTube Music credentials
                const authorization = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicAuth) || '';
                const cookie = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicCookie) || '';

                // First check if we can get the last song to verify authorization
                try {
                    await JC_Get<SongModel>(SongModel, "ytGetLastSongFromHistory", {
                        ytMusicAuth: authorization,
                        ytMusicCookie: cookie
                    });
                    // If successful, we're authorized
                    setIsAuthorized(true);
                } catch (error) {
                    // If this fails due to authorization, don't continue loading the page
                    if (error instanceof Error && error.message.includes("YouTube Music authentication failed")) {
                        setIsAuthorized(false);
                        setIsInitialised(true);
                        return;
                    }
                }

                // Continue loading the page if authorized
                const results = await Promise.all([
                    JC_Get<UserConfigModel>(UserConfigModel, "userConfig", { userId: userId }),
                    JC_GetRaw<UserArtistSubscriptionModel[]>("userArtistSubscription/getList", { userId: userId }),
                    JC_GetRaw<ArtistModel[]>("ytGetSubscriptions", {
                        ytMusicAuth: authorization,
                        ytMusicCookie: cookie
                    })
                ]);

                let dbUserConfig:UserConfigModel = results[0] as UserConfigModel;
                await yourPlaylistUrlChanged(dbUserConfig ?? D_UserConfig(userId));
                let userSubsResult = results[1] as UserArtistSubscriptionModel[];
                let ytSubsResult = results[2] as ArtistModel[];

                // Set 'Artist' on every User Subscription
                ytSubsResult.filter(ytSub => userSubsResult.find(s => s.ArtistId == ytSub.Id) != null).forEach(ytSub => {
                    userSubsResult.find(s => s.ArtistId == ytSub.Id)!.Artist = ytSub;
                });
                // Delete any User Subsriptions where not subscribed on ytMusic anymore
                userSubsResult = userSubsResult.filter(s => s.Artist != null);
                userSubsResult.sort((a,b) => a.Artist!.Name.trim().toLowerCase() > b.Artist!.Name.trim().toLowerCase() ? 1 : -1);
                setSubscriptions(userSubsResult);
                // Resume current process if still running
                let runningProcess = await checkExistingProcess(dbUserConfig ?? D_UserConfig(userId));
                // IF not resuming process, create any UserArtistSubscriptions that don't exist yet for each ytSub
                if (!runningProcess) {
                    createNewSubsProcess(ytSubsResult, userSubsResult);
                }
                setIsInitialised(true);
            } catch (error) {
                console.error("Error initializing page:", error);
                setIsAuthorized(false);
                setIsInitialised(true);
            }
        };

        // Start initialization
        initializePage();
    }, []);


    // - HANDLES - //

    // Your playlist URL change
    async function yourPlaylistUrlChanged(newUserConfig:UserConfigModel) {
        setUserConfig(newUserConfig);
        setYourPlaylistData(null);
        setYourPlaylistName(null);
        setYourPlaylistError("");
        setProcessHasRan(false);
        if (!JC_Utils.stringNullOrEmpty(newUserConfig.NominatedNewSongsPlaylistUrl)) {
            setIsPlaylistUrlLoading(true);
            let playlist:PlaylistModel|null = null;
            try {
                // Get YouTube Music credentials
                const authorization = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicAuth) || '';
                const cookie = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicCookie) || '';
                playlist = await JC_Get(PlaylistModel, "playlist", {
                    playlistUrl: newUserConfig.NominatedNewSongsPlaylistUrl,
                    ytMusicAuth: authorization,
                    ytMusicCookie: cookie
                });
            } catch (e) {}
            if (playlist != null) {
                setYourPlaylistData(playlist);
                setYourPlaylistName(playlist?.Title);
            } else {
                setYourPlaylistError("Playlist URL is invalid!");
            }
            setIsPlaylistUrlLoading(false);
        }
    }

    // Check and continue existing process that may be half-done
    async function checkExistingProcess(currentUserConfig: UserConfigModel) {
        let dbProgress = await JC_Get<UserProcessProgressModel>(UserProcessProgressModel, "userProcessProgress", { userId: userId, processCode: UserProcessProgressCodeEnum.NewSongs });
        if (dbProgress?.ProgressData != null && dbProgress.ProgressData.ItemsLeftJson?.length > 0) {
            setTimeout(() => process(dbProgress, false, currentUserConfig));
            return true;
        } else {
            return false;
        }
    }

    // Creating new UserArtistSubscriptions process
    async function createNewSubsProcess(ytSubs:ArtistModel[], userSubs:UserArtistSubscriptionModel[]) {
        let newYtSubs = ytSubs.filter(ytSub => userSubs.find(s => s.ArtistId == ytSub.Id) == null);
        if (newYtSubs.length > 0) {
            setIsLoading(true);
            setProcessHasRan(true);
            setInCreatingSubsMode(true);
            let tempProgressItems:UserArtistSubscriptionModel[] = [];
            setProgressItems(tempProgressItems);

            for (const ytSub of newYtSubs) {
                // Cache image for the artist that will be displayed
                const cachedArtist = await JC_PostRaw("imageCache", [ytSub]);
                const artistWithCachedImage = cachedArtist[0] || ytSub;

                let newUserSub:UserArtistSubscriptionModel = {
                    ...D_UserArtistSubscription(userId),
                    ArtistId: ytSub.Id,
                    ArtistName: ytSub.Name,
                    Artist: artistWithCachedImage
                };
                tempProgressItems.push(newUserSub);
                setProgressItems([...tempProgressItems]);
                // Get YouTube Music credentials
                const authorization = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicAuth) || '';
                const cookie = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicCookie) || '';
                let fullArtist:ArtistFullModel = await JC_Get(ArtistFullModel, "ytGetArtist", {
                    artistUrl: `https://music.youtube.com/channel/${ytSub.Id}`,
                    ytMusicAuth: authorization,
                    ytMusicCookie: cookie
                });
                let singleIds = fullArtist.Singles.map(s => s.Id);
                let albumIds = fullArtist.Albums.map(a => a.Id);
                newUserSub = {
                    ...newUserSub,
                    CheckedSingleIdList: singleIds,
                    CheckedSingleIdListJson: JSON.stringify(singleIds),
                    CheckedAlbumIdList: albumIds,
                    CheckedAlbumIdListJson: JSON.stringify(albumIds)
                }
                userSubs.push(newUserSub);
                JC_Post(UserArtistSubscriptionModel, "userArtistSubscription", newUserSub);
            }
            // Sort updated Subcriptions list by Artist name
            userSubs.sort((a,b) => a.Artist!.Name.toLowerCase() > b.Artist!.Name.toLowerCase() ? 1 : -1);
            setSubscriptions(userSubs);

            // Check for any new Subscriptions and continue process if any
            // Get YouTube Music credentials
            const authorization = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicAuth) || '';
            const cookie = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicCookie) || '';

            let results = await Promise.all([
                JC_GetRaw<UserArtistSubscriptionModel[]>("userArtistSubscription/getList", { userId: userId }),
                JC_GetRaw<ArtistModel[]>("ytGetSubscriptions", {
                    ytMusicAuth: authorization,
                    ytMusicCookie: cookie
                })
            ]);
            let userSubsResult = results[0] as UserArtistSubscriptionModel[];
            let ytSubsResult = results[1] as ArtistModel[];

            // Replace ImageUrls with cached blob URLs for new subscriptions that will be processed
            const newSubs = ytSubsResult.filter(ytSub => userSubsResult.find(s => s.ArtistId == ytSub.Id) == null);
            const cachedNewSubs = await JC_PostRaw("imageCache", newSubs);

            userSubsResult.push(...userSubs); // Push the last ones that were just added in case any of the JC_Post calls hadn't finished yet
            await createNewSubsProcess(cachedNewSubs, tempProgressItems);

            // Finished
            setIsLoading(false);
        }
    }

    // Run
    async function process(existingProgress?:UserProcessProgressModel, updateUserConfig:boolean = true, passedUserConfig?: UserConfigModel) {

        // Use passed userConfig or fall back to state userConfig
        const currentUserConfig = passedUserConfig || userConfig;

        setIsLoading(true);
        setProcessHasRan(true);
        setProgressItems([]);
        setInCreatingSubsMode(false);

        // Update nominated playlist in UserConfig
        if (updateUserConfig && currentUserConfig) {
            currentUserConfig.NewSongsProcessDateLastRun = new Date();
            JC_Post(UserConfigModel, "userConfig", currentUserConfig);
        }

        // Get progress
        let dbProgress:UserProcessProgressModel = existingProgress ?? await JC_Get<UserProcessProgressModel>(UserProcessProgressModel, "userProcessProgress", { userId: userId, processCode: UserProcessProgressCodeEnum.NewSongs });

        let subscriptionsCompleted: UserArtistSubscriptionModel[] = [];
        let subscriptionsLeft: UserArtistSubscriptionModel[] = [];

        if (dbProgress?.ProgressData == null || dbProgress.ItemsLeftJson == null || JSON.parse(dbProgress.ItemsLeftJson).length == 0) {
            let notCreatedYet = dbProgress?.ProgressData == null;
            let progressData:ProgressData_NewSongs = {
                YourPlaylistUrl: currentUserConfig?.NominatedNewSongsPlaylistUrl!
            };

            subscriptionsCompleted = [];
            subscriptionsLeft = subscriptions;

            dbProgress = {
                ...(notCreatedYet ? D_UserProcessProgress(userId) : dbProgress),
                ProcessCode: UserProcessProgressCodeEnum.NewSongs,
                ProgressDataJson: JSON.stringify(progressData),
                ItemsCompletedJson: JSON.stringify(subscriptionsCompleted),
                ItemsLeftJson: JSON.stringify(subscriptionsLeft),
                ProgressData: progressData
            };
            setProgressItems(subscriptionsCompleted);
            await JC_Post(UserProcessProgressModel, "userProcessProgress", dbProgress);
        } else {
            // Deserialize existing progress
            subscriptionsCompleted = dbProgress.ItemsCompletedJson ? JSON.parse(dbProgress.ItemsCompletedJson) : [];
            subscriptionsLeft = dbProgress.ItemsLeftJson ? JSON.parse(dbProgress.ItemsLeftJson) : [];
            setProgressItems(subscriptionsCompleted);
        }

        // - TESTING - //
        // subscriptionsLeft = subscriptionsLeft.filter((s:any) => s.ArtistId == 'UCWR_-sd1qBK7HtJTgw0Djkw');
        // - TESTING - //

        // Process
        while (subscriptionsLeft.length > 0) {
            // Get next 10 subscriptions
            let thisLotOfSubs:UserArtistSubscriptionModel[] = [];
            [...Array(10)].forEach(() => {
                if (subscriptionsLeft.length > 0) {
                    thisLotOfSubs.push(subscriptionsLeft.shift()!);
                }
            });

            // Cache images for artists that are being added to the displayed progress list
            const artistsToCache = thisLotOfSubs.map(sub => sub.Artist!).filter(artist => artist != null);
            if (artistsToCache.length > 0) {
                const cachedArtists = await JC_PostRaw("imageCache", artistsToCache);
                // Update the cached URLs back to the subscriptions
                cachedArtists.forEach(cached => {
                    const subscription = thisLotOfSubs.find(sub => sub.Artist && sub.Artist.Id === cached.Id);
                    if (subscription && subscription.Artist) {
                        subscription.Artist.ImageUrl = cached.ImageUrl;
                    }
                });
            }

            subscriptionsCompleted.push(...thisLotOfSubs);
            setProgressItems([...subscriptionsCompleted]);
            // Get full Artists
            // Get YouTube Music credentials
            const authorization = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicAuth) || '';
            const cookie = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicCookie) || '';
            let getArtistCalls:ArtistFullModel[] = await Promise.all( thisLotOfSubs.map(sub => JC_Get(ArtistFullModel, "ytGetArtist", {
                artistUrl: `https://music.youtube.com/channel/${sub.ArtistId}`,
                ytMusicAuth: authorization,
                ytMusicCookie: cookie
            })) ) as ArtistFullModel[];
            for (const [i, fullArtist] of getArtistCalls.entries()) {
                // Add any new items
                let newSingles = fullArtist.Singles.filter(s => thisLotOfSubs[i].CheckedSingleIdList?.find(sId => sId == s.Id) == null && (currentUserConfig!.NewSongsProcessDateLastRun == null || s.Year! >= currentUserConfig!.NewSongsProcessDateLastRun.getFullYear()));
                let newAlbums = fullArtist.Albums.filter(a => thisLotOfSubs[i].CheckedAlbumIdList?.find(sId => sId == a.Id) == null && (currentUserConfig!.NewSongsProcessDateLastRun == null || a.Year! >= currentUserConfig!.NewSongsProcessDateLastRun.getFullYear()));

                // Replace ImageUrls with cached blob URLs for new items that will be rendered
                const cachedNewSingles = await JC_PostRaw("imageCache", newSingles);
                const cachedNewAlbums = await JC_PostRaw("imageCache", newAlbums);

                thisLotOfSubs[i].NewSingles = [];
                for (const single of cachedNewSingles) {
                    thisLotOfSubs[i].NewSingles.push(single);
                    setProgressItems([...subscriptionsCompleted]);
                    await YtAddItem(dbProgress.ProgressData!.YourPlaylistUrl, single.Id);
                }
                thisLotOfSubs[i].NewAlbums = [];
                for (const album of cachedNewAlbums) {
                    thisLotOfSubs[i].NewAlbums.push(album);
                    setProgressItems([...subscriptionsCompleted]);
                    await YtAddItem(dbProgress.ProgressData!.YourPlaylistUrl, album.Id);
                }
                // Update User Subscriptions with any new Singles or Albums
                if (newSingles.length > 0 || newAlbums.length > 0) {
                    thisLotOfSubs[i].CheckedSingleIdListJson = JSON.stringify(fullArtist.Singles.map(s => s.Id));
                    thisLotOfSubs[i].CheckedAlbumIdListJson = JSON.stringify(fullArtist.Albums.map(s => s.Id));
                    await JC_Post(UserArtistSubscriptionModel, "userArtistSubscription", thisLotOfSubs[i]);
                }
                thisLotOfSubs[i].Checked = true;
                setProgressItems([...subscriptionsCompleted]);
            }
            // Update progress using the new AdjustItems function
            await UserProcessProgressModel.AdjustItems(userId, UserProcessProgressCodeEnum.NewSongs, thisLotOfSubs);
            // Reset Subscriptions list
            setSubscriptions(subscriptionsCompleted);
        }

        // Make sure Subscriptions updated properly
        // Get YouTube Music credentials
        const finalAuth = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicAuth) || '';
        const finalCookie = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicCookie) || '';

        Promise.all([
            JC_GetRaw<UserArtistSubscriptionModel[]>("userArtistSubscription/getList", { userId: userId }),
            JC_GetRaw<ArtistModel[]>("ytGetSubscriptions", {
                ytMusicAuth: finalAuth,
                ytMusicCookie: finalCookie
            })
        ]).then(async results => {
            let userSubsResult = results[0] as UserArtistSubscriptionModel[];
            let ytSubsResult = results[1] as ArtistModel[];

            // Set 'Artist' on every User Subscription
            ytSubsResult.filter(ytSub => userSubsResult.find(s => s.ArtistId == ytSub.Id) != null).forEach(ytSub => {
                userSubsResult.find(s => s.ArtistId == ytSub.Id)!.Artist = ytSub;
            });
            userSubsResult = userSubsResult.filter(s => s.Artist != null);
            userSubsResult.sort((a,b) => a.Artist!.Name.trim().toLowerCase() > b.Artist!.Name.trim().toLowerCase() ? 1 : -1);
            setSubscriptions(userSubsResult);
            // Finished
            setIsLoading(false);
        });
    }


    // - MAIN - //

    // If not authorized, show only an error message without any other UI elements
    if (!isAuthorized && isInitialised) {
        return (
            <div className={styles.unauthorizedContainer}>
                <div className={styles.unauthorizedMessage}>
                    <p>YouTube Music authentication failed.</p>
                    <p>Please reset your token to use this feature.</p>
                </div>
            </div>
        );
    }

    return !isInitialised ? <JC_Spinner /> : <div className={styles.mainContainer}>

        {/* Title */}
        <JC_Title title="Get New Songs" />

        {/* Top Buttons */}
        <div className={styles.topButtonsContainer}>
            {/* Run */}
            <JC_Button
                text="Run"
                isSecondary
                onClick={() => process()}
                isDisabled={JC_Utils.stringNullOrEmpty(yourPlaylistName) || isLoading || isPlaylistUrlLoading}
            />
            {/* Subscriptions */}
            <JC_Button text="Subscriptions" linkToPage="https://music.youtube.com/library/subscriptions" linkInNewTab linkIsAbsolute isSecondary isSmall />
        </div>

        {/* Process */}
        <div id="processContainer" className={`${styles.processContainer} ${!processHasRan && (JC_Utils.stringNullOrEmpty(yourPlaylistName) || isLoading || isPlaylistUrlLoading) ? styles.waiting : ""}`}>
            {!processHasRan && !(JC_Utils.stringNullOrEmpty(yourPlaylistName) || isLoading || isPlaylistUrlLoading) && <div className={styles.readyText}>READY</div>}
            {processHasRan &&
            <React.Fragment>
                {inCreatingSubsMode  && <div style={{ marginBottom: "20px", fontSize: "20px" }}>Found new subscriptions...</div>}
                {!inCreatingSubsMode && <div style={{ marginBottom: "20px", fontSize: "20px" }}>Checking for new songs...</div>}
                {(showNew ? progressItems.filter(thisSub => (thisSub.NewSingles?.length??0) > 0 || (thisSub.NewAlbums?.length??0) > 0) : progressItems).map(thisSub =>
                <React.Fragment key={thisSub.Id}>
                    <Link className={`${styles.progressItem} ${thisSub.Checked ? styles.checked : ""} ${(thisSub.NewSingles?.length??0) > 0 || (thisSub.NewAlbums?.length??0) > 0 ? styles.hasNewItems : ""}`} href={`https://music.youtube.com/channel/${thisSub.ArtistId}`} target="_blank">
                        <Image
                            className={styles.itemImage}
                            src={thisSub.Artist!.ImageUrl}
                            width={100}
                            height={100}
                            alt="AlbumCover"
                            unoptimized
                        />
                        <div className={styles.progressTitle}>{thisSub.Artist!.Name}</div>
                    </Link>
                    {thisSub.NewSingles?.map(single =>
                    <Link key={single.Id} className={styles.progressItem} href={`https://music.youtube.com/playlist?list=${single.PlaylistId}`} target="_blank">
                        <div className={styles.itemGroupHeading}>SINGLE</div>
                        <div className={styles.newItem}>
                            <Image
                                className={styles.itemImage}
                                src={single.ImageUrl}
                                width={100}
                                height={100}
                                alt="AlbumCover"
                                unoptimized
                            />
                            <div className={styles.progressTitle}>{single.Title}</div>
                        </div>
                    </Link>)}
                    {thisSub.NewAlbums?.map(album =>
                    <Link key={album.Id} className={styles.progressItem} href={`https://music.youtube.com/playlist?list=${album.PlaylistId}`} target="_blank">
                        <div className={styles.itemGroupHeading}>ALBUM</div>
                        <div className={styles.newItem}>
                            <Image
                                className={styles.itemImage}
                                src={album.ImageUrl}
                                width={100}
                                height={100}
                                alt="AlbumCover"
                                unoptimized
                            />
                            <div className={styles.progressTitle}>{album.Title}</div>
                        </div>
                    </Link>)}
                </React.Fragment>)}
                {!isLoading && <div style={{ marginTop: "20px", fontSize: "20px" }}>Finished!</div>}
            </React.Fragment>}
        </div>

        {/* Form Fields */}
        <div className={styles.fieldsContainer}>
            {!(inCreatingSubsMode && isLoading) && (
                <>
                    {/* Your Playlist URL */}
                    <div className={styles.fieldWithSpinner}>
                        <JC_Field
                            inputOverrideClass={styles.inputOverride}
                            inputId="newSongsPlaylisitUrl"
                            type={FieldTypeEnum.Text}
                            label="Your Playlist To Add New Songs To"
                            readOnly={isLoading || isPlaylistUrlLoading}
                            value={userConfig?.NominatedNewSongsPlaylistUrl ?? ""}
                            onFocus={() => (document.getElementById("newSongsPlaylisitUrl") as HTMLInputElement).select()}
                            onChange={(newValue: string) => yourPlaylistUrlChanged({ ...userConfig!, NominatedNewSongsPlaylistUrl: newValue })}
                            immediateValidate={() => yourPlaylistError}
                            validate={(v: any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a playlist url." : ""}
                        />
                        {isPlaylistUrlLoading && <JC_Spinner overrideClass={styles.fieldSpinner} isSmall />}
                    </div>

                    {/* To Playlist Display */}
                    {yourPlaylistData ? (
                        <div className={styles.playlistTileContainer}>
                            <div
                                className={styles.playlistTile}
                                onClick={() => window.open(userConfig?.NominatedNewSongsPlaylistUrl!, '_blank')}
                            >
                                <Image
                                    className={styles.playlistTileImage}
                                    src={yourPlaylistData.ImageUrl || '/default-playlist.png'}
                                    width={70}
                                    height={70}
                                    alt="PlaylistCover"
                                    unoptimized
                                />
                                <div className={styles.playlistTileTitle}>{yourPlaylistData.Title}</div>
                            </div>
                        </div>
                    ) : (
                        <div className={styles.playlistTileContainer}>
                            <div className={styles.playlistTilePlaceholder}>
                                <div className={styles.playlistTilePlaceholderImage}></div>
                                <div className={styles.playlistTilePlaceholderTitle}></div>
                            </div>
                        </div>
                    )}

                    {session.data == null && <div className={styles.notSignedInText}>Since you are not signed in, only the first 5 susbcriptions are used.</div>}
                </>
            )}
        </div>

        {/* Show New */}
        {processHasRan && !inCreatingSubsMode &&
        <div className={styles.showNewContainer}>
            <JC_Checkbox label="Show only new" checked={showNew} onChange={() => setShowNew(!showNew)} />
        </div>}

    </div>;
}
