import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { GetPlaylist } from "./business";

export async function GET(request: NextRequest) {

    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const playlistUrl = params.get("playlistUrl")!;
        const ytMusicAuth = params.get("ytMusicAuth") || '';
        const ytMusicCookie = params.get("ytMusicCookie") || '';

        const result = await GetPlaylist(playlistUrl, ytMusicAuth, ytMusicCookie);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}