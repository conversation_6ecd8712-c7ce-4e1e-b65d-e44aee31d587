import { sql } from "@vercel/postgres";
import { UserProcessProgressModel } from "@/app/models/UserProcessProgress";
import { UserProcessProgressCodeEnum } from "@/app/enums/UserProcessProgressCode";


// - GET - //

export async function GetUserProcessProgress(userId:string, processCode:UserProcessProgressCodeEnum):Promise<UserProcessProgressModel> {
    let dbUserProcessProgress:UserProcessProgressModel = (await sql<UserProcessProgressModel>`
        SELECT "Id",
               "UserId",
               "ProcessCode",
               "ProgressDataJson",
               "CreatedAt",
               "ModifiedAt",
               "Deleted"
        FROM public."UserProcessProgress"
        WHERE "UserId" = ${userId} AND "ProcessCode" = ${processCode}
    `).rows[0];
    if (dbUserProcessProgress?.ProgressDataJson != null) {
        dbUserProcessProgress.ProgressData = JSON.parse(dbUserProcessProgress.ProgressDataJson);
    }
    return dbUserProcessProgress;
}


// - CREATE - //

export async function CreateUserProcessProgress(userProcessProgressData:UserProcessProgressModel) {
    await sql`
        INSERT INTO public."UserProcessProgress"
        (
            "Id",
            "UserId",
            "ProcessCode",
            "ProgressDataJson",
            "CreatedAt"
        )
        VALUES
        (
            ${userProcessProgressData.Id},
            ${userProcessProgressData.UserId},
            ${userProcessProgressData.ProcessCode},
            ${userProcessProgressData.ProgressDataJson},
            ${new Date().toUTCString()}
        )
    `;
}


// - UPDATE - //

export async function UpdateUserProcessProgress(userProcessProgressData:UserProcessProgressModel) {
    await sql`
        UPDATE public."UserProcessProgress"
        SET "UserId"            = ${userProcessProgressData.UserId},
            "ProcessCode"       = ${userProcessProgressData.ProcessCode},
            "ProgressDataJson"  = ${userProcessProgressData.ProgressDataJson},
            "ModifiedAt"        = ${new Date().toUTCString()},
            "Deleted"           = ${userProcessProgressData.Deleted}
        WHERE "Id" = ${userProcessProgressData.Id}
    `;
}