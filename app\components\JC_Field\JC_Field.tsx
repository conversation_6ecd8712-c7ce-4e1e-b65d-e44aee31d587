"use client"

import styles from "./JC_Field.module.scss";
import React, { useState } from 'react';
import { Color } from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import { EditorProvider, useCurrentEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { JC_Utils } from '@/app/Utils';
import { JC_FieldModel } from '@/app/models/ComponentModels/JC_Field';
import { FieldTypeEnum } from '@/app/enums/FieldType';

// Format on change
function formatNumberInputChange(event:any):string {
    let inputString = event.target.value;
    // Only allow numbers and .'s
    if (!['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.'].includes(inputString.slice(-1))) {
        inputString = inputString.slice(0, inputString.length-1);
    }
    // Only allow 2dp
    if (inputString.indexOf('.') > -1) {
        inputString = inputString.substring(0, inputString.indexOf('.') + 3);
    }
    return inputString;
}

// Format on blur
function formatNumberInputBlur(event:any, defaultValue:number, onChange?:(newValue:string) => void):number {
    let inputString = event.target.value;
    // Remove . at end
    if (inputString.slice(-1) == '.') {
        inputString = inputString.slice(0, inputString.length-1);
    }
    // IF two .'s, only keep 1st one and remove rest of string after 2nd .
    if (inputString.split('.').length > 2) {
        inputString = inputString.split('.')[0] + '.' + inputString.split('.')[1];
    }
    if (JC_Utils.stringNullOrEmpty(inputString) && defaultValue != null) {
        onChange != null && onChange(String(defaultValue));
        return defaultValue;
    } else {
        return +inputString;
    }
}


// Color picker
let colorPickerOpen = false;
let currentHtml = null;


export default function JC_Field(_: Readonly<JC_FieldModel>) {

    // - STATE - //

    const [thisValue, setThisValue] = useState<string | number>(_.value ?? "");


    // - STYLES - //

    let inputStyle = '';
    switch(_.type) {
        case FieldTypeEnum.Text:     inputStyle = styles.textType; break;
        case FieldTypeEnum.Email:    inputStyle = styles.textType; break;
        case FieldTypeEnum.Number:   inputStyle = styles.numberType; break;
        case FieldTypeEnum.Password: inputStyle = styles.passwordType; break;
        case FieldTypeEnum.Textarea: inputStyle = styles.textareaType; break;
        case FieldTypeEnum.RichText: inputStyle = styles.richTextType; break;
    }


    // - HANDLES - //

    // onChange
    function handleOnChange(event:any) {
        if (_.type == FieldTypeEnum.Number) {
            event.target.value = formatNumberInputChange(event);
        }
        setThisValue(event.target.value);
        _.onChange != null && _.onChange(event.target.value);
    }

    // onBlur
    function handleOnBlur(event:any) {
        if (_.type == FieldTypeEnum.Number) {
            event.target.value = formatNumberInputBlur(event, _.defaultValue as number, _.onChange)
        }
        _.onBlur != null && _.onBlur(event.target.value);
    }

    // onKeyDown
    function handleKeyDown(event:any) {
        if (event.code == "Enter" && _.onEnter != null)
            _.onEnter(event);
        if (event.code == "Escape" && _.onEscape != null)
            _.onEscape(event);
    }


    // - BUILD - //

    // Rich Text Buttons
    const MenuBar = () => {
        const { editor } = useCurrentEditor()
        if (!editor) return null;
        return (
            <div className={styles.richTextButtons}>
                {_.richTextEnableColor &&
                <input
                    className={styles.colourButton}
                    type="color"
                    onFocus={() => colorPickerOpen = true}
                    id="THE_INPUT"
                    onChange={event => {
                        editor.chain().focus().setColor(event.target.value).run();
                        currentHtml = editor?.getHTML();
                    }}
                    value={editor.getAttributes('textStyle').color}
                    data-testid="setColor"
                />}
                {_.richTextEnableBold &&
                <button
                    className={editor.isActive('bold') ? styles.isActive : ''}
                    onClick={() => editor.chain().focus().toggleBold().run()}
                    disabled={!editor.can().chain().focus().toggleBold().run()}
                >
                    B
                </button>}
                {_.richTextEnableItalic &&
                <button
                    className={editor.isActive('italic') ? styles.isActive : ''}
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                    disabled={!editor.can().chain().focus().toggleItalic().run()}
                >
                    <i>I</i>
                </button>}
                {/* <button
                    onClick={() => editor.chain().focus().undo().run()}
                    disabled={!editor.can().chain().focus().undo().run()}
                >
                    Undo
                </button>
                <button
                    onClick={() => editor.chain().focus().redo().run()}
                    disabled={!editor.can().chain().focus().redo().run()}
                >
                    Redo
                </button> */}
            </div>
        );
    }


    // - MAIN - //

    return (
        <div className={`${styles.mainContainer} ${_.readOnly ? styles.readOnly : ''} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>

            {/* Label */}
            {!JC_Utils.stringNullOrEmpty(_.label) &&
            <div className={styles.label}>
                {_.label}
                {_.immediateValidate != null && !JC_Utils.stringNullOrEmpty(_.immediateValidate(_.value!))
                    ? <span className={styles.errorSpan}>{_.immediateValidate(_.value!)}</span>
                    : _.validate != null && !JC_Utils.stringNullOrEmpty(_.validate(_.value!)) && <span className={styles.errorSpan}>{_.validate(_.value!)}</span>}
            </div>}

            {/* Input */}
            <div
                className={`
                    ${styles.inputContainer} ${inputStyle}
                    ${!JC_Utils.stringNullOrEmpty(_.inputOverrideClass) ? _.inputOverrideClass : ''}
                `}
                onClick={_.onClick}
            >

                {_.type == FieldTypeEnum.RichText
                    ?
                    <EditorProvider
                        slotBefore={<MenuBar />}
                        extensions={[StarterKit.configure(), Color, TextStyle]}
                        content={_.value?.toString()}
                        onBlur={(props) => {
                            if (colorPickerOpen) {
                                _.onChange!(props.editor.getHTML());
                                colorPickerOpen = false;
                            }
                        }}
                        onUpdate={(props:any) => !colorPickerOpen && _.onChange!(props.editor.getHTML())}
                    />
                    :
                    _.type == FieldTypeEnum.Textarea
                        ?
                        <textarea
                            defaultValue={_.value}
                            onChange={handleOnChange}
                            onBlur={handleOnBlur}
                            disabled={_.readOnly}
                            id={_.inputId}
                        />
                        :
                        <input
                            type={ _.type == FieldTypeEnum.Email    ? "email"
                                :  _.type == FieldTypeEnum.Password ? "password"
                                :                                     "text"}
                            placeholder={_.placeholder}
                            value={_.readOnly ? _.value : thisValue}
                            onFocus={_.onFocus}
                            onChange={handleOnChange}
                            onBlur={handleOnBlur}
                            onKeyDown={(event) => handleKeyDown(event)}
                            disabled={_.readOnly}
                            id={_.inputId}
                        />}

            </div>

        </div>
    );
}