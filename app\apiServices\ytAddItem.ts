import { JC_PostRaw } from "./JC_PostRaw";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";

const resetIntervalMins = 30;

export async function YtAddItem(playlistUrl:string, itemId:string) {
    try {
        // Get YouTube Music credentials
        const authorization = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicAuth) || '';
        const cookie = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicCookie) || '';

        // Call API with auth credentials
        await JC_PostRaw("ytAddArtistItemToPlaylist", {
            playlistUrl: playlistUrl,
            itemId: itemId,
            ytMusicAuth: authorization,
            ytMusicCookie: cookie
        });
    } catch (error) {
        // IF timed out, just do call again, this one will be quicker since songs already added will have quick calls
        console.log("Error! Trying again...", error);
        await YtAddItem(playlistUrl, itemId);
    }
}