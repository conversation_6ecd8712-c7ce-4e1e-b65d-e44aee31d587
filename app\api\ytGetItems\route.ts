import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { YT_GetItems } from "./business";
import { YtMusicItemTypeEnum } from "@/app/enums/YtMusicItemType";

export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        // Get request data from query parameters
        const params = new URL(request.url).searchParams;
        const bodyDataStr = params.get('bodyData');
        const typeStr = params.get('type');
        const ytMusicAuth = params.get('ytMusicAuth');
        const ytMusicCookie = params.get('ytMusicCookie');

        // Parse bodyData from JSON string
        const bodyData = bodyDataStr ? JSON.parse(bodyDataStr) : null;

        // Validate required parameters
        if (!bodyData || !typeStr) {
            return NextResponse.json({ error: "Missing required parameters" }, { status: 400 });
        }

        if (!ytMusicAuth || !ytMusicCookie) {
            return NextResponse.json({ error: "Missing YouTube Music authentication credentials" }, { status: 400 });
        }

        // Convert string to enum
        const type = YtMusicItemTypeEnum[typeStr as keyof typeof YtMusicItemTypeEnum];

        // Call business logic with auth credentials
        const result = await YT_GetItems(bodyData, type, ytMusicAuth, ytMusicCookie);

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
