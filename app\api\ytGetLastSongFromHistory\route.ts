import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { YT_GetLastSongFromHistory, YtMusicUnauthorizedError } from "./business";

export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        // Get auth credentials from query parameters
        const params = new URL(request.url).searchParams;
        const ytMusicAuth = params.get('ytMusicAuth');
        const ytMusicCookie = params.get('ytMusicCookie');

        // Validate required parameters
        if (!ytMusicAuth || !ytMusicCookie) {
            return NextResponse.json({ error: "Missing YouTube Music authentication credentials" }, { status: 400 });
        }

        // Call business logic with auth credentials
        const result = await YT_GetLastSongFromHistory(ytMusicAuth, ytMusicCookie);

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);

        // Check if this is an unauthorized error
        if (error instanceof YtMusicUnauthorizedError) {
            return NextResponse.json({
                error: error.message,
                unauthorized: true
            }, { status: 401 });
        }

        return NextResponse.json({ error }, { status: 500 });
    }
}